#!/usr/bin/env python3
"""
Basic upscaling demo for Chain-of-Zoom without requiring gated models.
This demonstrates the image processing pipeline and basic upscaling techniques.
"""

import os
import argparse
from PIL import Image
import torch
from torchvision import transforms
import numpy as np

def resize_and_center_crop(img: Image.Image, size: int) -> Image.Image:
    """Resize and center crop image to specified size."""
    w, h = img.size
    scale = size / min(w, h)
    new_w, new_h = int(w * scale), int(h * scale)
    img = img.resize((new_w, new_h), Image.LANCZOS)
    left = (new_w - size) // 2
    top = (new_h - size) // 2
    return img.crop((left, top, left + size, top + size))

def basic_upscale(image: Image.Image, scale_factor: int, method: str = "bicubic") -> Image.Image:
    """Basic upscaling using traditional methods."""
    w, h = image.size
    new_w, new_h = w * scale_factor, h * scale_factor
    
    if method == "bicubic":
        return image.resize((new_w, new_h), Image.BICUBIC)
    elif method == "lanczos":
        return image.resize((new_w, new_h), Image.LANCZOS)
    elif method == "nearest":
        return image.resize((new_w, new_h), Image.NEAREST)
    else:
        raise ValueError(f"Unknown method: {method}")

def simulate_chain_of_zoom(image: Image.Image, scale_factor: int, num_steps: int, process_size: int = 512):
    """Simulate the Chain-of-Zoom process using basic upscaling."""
    print(f"🔄 Simulating Chain-of-Zoom with {num_steps} steps, {scale_factor}x scale factor")
    
    # Start with processed image
    current_image = resize_and_center_crop(image, process_size)
    results = [current_image.copy()]
    
    print(f"Step 0: {current_image.size}")
    
    for step in range(num_steps):
        # Simulate the zoom-in process
        w, h = current_image.size
        crop_size = w // scale_factor
        
        # Center crop to simulate zooming in
        left = (w - crop_size) // 2
        top = (h - crop_size) // 2
        cropped = current_image.crop((left, top, left + crop_size, top + crop_size))
        
        # Upscale back to original size
        upscaled = basic_upscale(cropped, scale_factor, "bicubic")
        
        results.append(upscaled.copy())
        current_image = upscaled
        
        print(f"Step {step + 1}: {upscaled.size}")
    
    return results

def test_ram_captioning(image_path: str):
    """Test RAM model for image captioning (if available)."""
    try:
        from ram.models.ram_lora import ram
        from ram import inference_ram as inference
        from torchvision import transforms
        
        print("🔍 Testing RAM model for image captioning...")
        
        # Load RAM model
        ram_model = ram(
            pretrained="ckpt/RAM/ram_swin_large_14m.pth",
            image_size=384,
            vit='swin_l'
        )
        ram_model.eval()
        
        # Prepare image
        image = Image.open(image_path).convert('RGB')
        ram_transforms = transforms.Compose([
            transforms.Resize((384, 384)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        image_tensor = ram_transforms(image).unsqueeze(0)
        
        # Generate caption
        with torch.no_grad():
            captions = inference(image_tensor, ram_model)
            print(f"📝 Generated caption: {captions[0]}")
            return captions[0]
            
    except Exception as e:
        print(f"⚠️ RAM captioning failed: {e}")
        return "high quality, detailed image"

def main():
    parser = argparse.ArgumentParser(description="Basic Chain-of-Zoom Demo")
    parser.add_argument('--input_image', '-i', type=str, default='samples/0064.png', 
                       help='Path to input image')
    parser.add_argument('--output_dir', '-o', type=str, default='demo_outputs',
                       help='Output directory')
    parser.add_argument('--scale_factor', type=int, default=4,
                       help='Scale factor for each step')
    parser.add_argument('--num_steps', type=int, default=3,
                       help='Number of zoom steps')
    parser.add_argument('--process_size', type=int, default=512,
                       help='Processing size')
    parser.add_argument('--test_ram', action='store_true',
                       help='Test RAM model captioning')
    
    args = parser.parse_args()
    
    print("🚀 Chain-of-Zoom Basic Demo")
    print("=" * 50)
    
    # Check input image
    if not os.path.exists(args.input_image):
        print(f"❌ Input image not found: {args.input_image}")
        return
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Load image
    print(f"📸 Loading image: {args.input_image}")
    image = Image.open(args.input_image).convert('RGB')
    print(f"   Original size: {image.size}")
    
    # Test RAM captioning if requested
    if args.test_ram:
        caption = test_ram_captioning(args.input_image)
        print(f"   Caption: {caption}")
    
    # Simulate Chain-of-Zoom process
    results = simulate_chain_of_zoom(
        image, 
        args.scale_factor, 
        args.num_steps, 
        args.process_size
    )
    
    # Save results
    print(f"\n💾 Saving results to {args.output_dir}")
    
    # Save individual steps
    for i, result in enumerate(results):
        output_path = os.path.join(args.output_dir, f"step_{i}.png")
        result.save(output_path)
        print(f"   Saved: step_{i}.png ({result.size})")
    
    # Create concatenated result
    print("\n🔗 Creating concatenated result...")
    total_width = sum(img.width for img in results)
    max_height = max(img.height for img in results)
    
    concat_image = Image.new('RGB', (total_width, max_height), (255, 255, 255))
    x_offset = 0
    
    for img in results:
        concat_image.paste(img, (x_offset, 0))
        x_offset += img.width
    
    concat_path = os.path.join(args.output_dir, "concatenated_result.png")
    concat_image.save(concat_path)
    print(f"   Saved: concatenated_result.png ({concat_image.size})")
    
    # Create comparison with simple upscaling
    print("\n📊 Creating comparison with simple upscaling...")
    original_processed = resize_and_center_crop(image, args.process_size)
    simple_upscale = basic_upscale(original_processed, args.scale_factor ** args.num_steps)
    
    comparison_width = max(results[-1].width, simple_upscale.width)
    comparison_height = results[-1].height + simple_upscale.height + 20
    
    comparison = Image.new('RGB', (comparison_width, comparison_height), (255, 255, 255))
    comparison.paste(results[-1], (0, 0))
    comparison.paste(simple_upscale, (0, results[-1].height + 20))
    
    comparison_path = os.path.join(args.output_dir, "comparison.png")
    comparison.save(comparison_path)
    print(f"   Saved: comparison.png (Chain-of-Zoom vs Simple upscaling)")
    
    print("\n✅ Demo completed successfully!")
    print(f"📁 Check outputs in: {args.output_dir}")
    print("\n💡 To use the full Chain-of-Zoom with AI models:")
    print("   1. Get Stable Diffusion 3 access on Hugging Face")
    print("   2. Run: huggingface-cli login")
    print("   3. Use: python inference_coz.py")

if __name__ == "__main__":
    main()
