# Chain-of-Zoom Setup Guide for macOS

## ✅ Installation Complete!

Your Chain-of-Zoom setup is now ready! Here's what has been installed and configured:

### 🛠️ Environment Setup
- **Virtual Environment**: `coz_env` (Python 3.13)
- **Location**: `/Users/<USER>/PyCharmMiscProject/Chain-of-Zoom/`
- **All Dependencies**: Successfully installed and tested

### 📦 Installed Packages
- PyTorch 2.7.0 (CPU version for macOS)
- Transformers, Diffusers, Accelerate
- Hugging Face Hub with CLI tools
- PEFT, LoRA libraries
- Image processing libraries (PIL, torchvision)
- All other required dependencies

### 🎯 Model Checkpoints
- ✅ **RAM Model**: Downloaded (5.24 GB)
- ✅ **SR_LoRA**: Available
- ✅ **SR_VAE**: Available  
- ✅ **DAPE**: Available

### 📸 Sample Images
- 5 high-resolution test images ready for processing
- Image processing pipeline tested and working

## 🚀 Next Steps

### 1. Get Access to Stable Diffusion 3

The main model requires access to Stable Diffusion 3 from Stability AI:

1. **Visit**: https://huggingface.co/stabilityai/stable-diffusion-3-medium-diffusers
2. **Request Access**: Click "Request access to this model"
3. **Wait for Approval**: Usually takes a few hours to a day

### 2. Authenticate with Hugging Face

Once you have access:

```bash
cd Chain-of-Zoom
source coz_env/bin/activate
huggingface-cli login
```

Enter your Hugging Face token when prompted.

### 3. Run Your First Super-Resolution

```bash
# Basic usage - 4x upscaling
python inference_coz.py --input_image samples/0064.png --output_dir outputs --upscale 4

# With custom prompt
python inference_coz.py --input_image samples/0064.png --output_dir outputs --upscale 4 --prompt "high quality, detailed"

# Recursive upscaling (multiple steps)
python inference_coz.py --input_image samples/0064.png --output_dir outputs --upscale 4 --rec_type recursive --rec_num 3
```

## 🎛️ Usage Options

### Key Parameters:
- `--input_image`: Path to input image
- `--output_dir`: Directory for outputs
- `--upscale`: Upscaling factor (2, 4, 8, etc.)
- `--rec_type`: Type of recursion (`onestep`, `recursive`, `recursive_multiscale`)
- `--rec_num`: Number of recursive steps
- `--prompt_type`: Prompt generation (`null`, `dape`, `vlm`)
- `--prompt`: Custom text prompt

### Recursion Types:
- **onestep**: Single upscaling pass
- **recursive**: Multiple passes, each using previous output
- **recursive_multiscale**: Multi-scale recursive approach

### Prompt Types:
- **null**: No automatic prompting
- **dape**: Use DAPE model for image captioning
- **vlm**: Use Vision-Language Model (Qwen2.5-VL)

## 🔧 Troubleshooting

### Common Issues:

1. **CUDA Not Available**: 
   - This is normal on macOS
   - The system will use CPU (slower but functional)

2. **Memory Issues**:
   - Use `--efficient_memory` flag
   - Reduce `--process_size` (default 512)

3. **Authentication Errors**:
   - Ensure you have SD3 access approved
   - Run `huggingface-cli login` again

### Performance Tips:
- Start with smaller images for testing
- Use `--mixed_precision fp16` to save memory
- Consider using `--rec_num 2` for faster results

## 📁 Output Structure

```
outputs/
├── per-sample/          # Individual image results
│   └── [image_name]/
│       ├── 0.png        # Original processed
│       ├── 1.png        # First upscale
│       ├── 2.png        # Second upscale
│       └── txt/         # Generated prompts
├── per-scale/           # Results by scale
│   ├── scale0/
│   ├── scale1/
│   └── scale2/
└── recursive/           # Concatenated results
```

## 🧪 Test Your Setup

Run the test script anytime to verify everything is working:

```bash
python test_setup.py
```

## 🎯 Example Workflows

### Quick Test:
```bash
python inference_coz.py -i samples/0064.png -o test_output --upscale 4 --rec_type onestep
```

### High-Quality Recursive:
```bash
python inference_coz.py -i samples/0064.png -o high_quality --upscale 4 --rec_type recursive --rec_num 3 --prompt_type dape
```

### Memory-Efficient:
```bash
python inference_coz.py -i samples/0064.png -o efficient --upscale 4 --efficient_memory --mixed_precision fp16
```

## 📚 Additional Resources

- **Original Paper**: Chain-of-Zoom: Extreme Super-Resolution
- **GitHub**: https://github.com/bryanswkim/Chain-of-Zoom
- **Hugging Face**: https://huggingface.co/stabilityai/stable-diffusion-3-medium-diffusers

---

**Status**: ✅ Setup Complete - Ready for SD3 Authentication
**Next**: Get Stable Diffusion 3 access and run `huggingface-cli login`
