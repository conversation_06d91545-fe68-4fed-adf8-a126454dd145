../../../bin/wgit,sha256=sPjGe3-nbHOp4ixmia2wXCCABzLqqaa719xxjYAeq9U,296
benchmarks/__init__.py,sha256=BBoPvMfJjZ0Fi25JCRS8sPYUfLbHneLncV3JeGDIGHg,198
benchmarks/__pycache__/__init__.cpython-313.pyc,,
benchmarks/__pycache__/fsdp.cpython-313.pyc,,
benchmarks/__pycache__/moe.cpython-313.pyc,,
benchmarks/__pycache__/oss.cpython-313.pyc,,
benchmarks/__pycache__/pipe.cpython-313.pyc,,
benchmarks/__pycache__/utils.cpython-313.pyc,,
benchmarks/datasets/__init__.py,sha256=BBoPvMfJjZ0Fi25JCRS8sPYUfLbHneLncV3JeGDIGHg,198
benchmarks/datasets/__pycache__/__init__.cpython-313.pyc,,
benchmarks/datasets/__pycache__/mnist.cpython-313.pyc,,
benchmarks/datasets/__pycache__/wikitext2_data.cpython-313.pyc,,
benchmarks/datasets/mnist.py,sha256=uhKBcN22-s2WowOYHO3IZpRG9gT_zrKgDdTqKlfLlok,1211
benchmarks/datasets/wikitext2_data.py,sha256=S0guG71HN63CqbE0Qc6p9ccfogiIlLnNOSKIIIs5s_0,4359
benchmarks/fsdp.py,sha256=bQ2j_RoztbB64PPzLnpdgO3N8HgGZeUUySVZcepseSk,14031
benchmarks/golden_configs/__init__.py,sha256=BBoPvMfJjZ0Fi25JCRS8sPYUfLbHneLncV3JeGDIGHg,198
benchmarks/golden_configs/__pycache__/__init__.cpython-313.pyc,,
benchmarks/golden_configs/__pycache__/lm_wikitext2.cpython-313.pyc,,
benchmarks/golden_configs/__pycache__/oss_mnist.cpython-313.pyc,,
benchmarks/golden_configs/lm_wikitext2.py,sha256=vJkUQt8Y3pbaJocKjEmYbbEKOjfW4_HFNmHlNVPL_ug,4897
benchmarks/golden_configs/oss_mnist.py,sha256=CCUnPoG_Hp7mTrIdNC2nr9s7RFHfAZjuJXE9GJW2o_c,528
benchmarks/models/__init__.py,sha256=BBoPvMfJjZ0Fi25JCRS8sPYUfLbHneLncV3JeGDIGHg,198
benchmarks/models/__pycache__/__init__.cpython-313.pyc,,
benchmarks/models/__pycache__/transformer_lm.cpython-313.pyc,,
benchmarks/models/transformer_lm.py,sha256=4l4sgO5roho7SJdDSB_7quF5zXjU-sY7NiQcGL47_qM,8443
benchmarks/moe.py,sha256=EVS6b16XNK5W4wt2dpJHWz1BNvV7MWfNco_hgMoC0aQ,4578
benchmarks/oss.py,sha256=F4rePx1jCJu_F_0h36hlWqdZXzCl74fZYIQBnQGlZyI,14495
benchmarks/pipe.py,sha256=g5T5oAVi1lCNfmCjqHyAehEfRdYeNOnvDjmauBKTU7E,11262
benchmarks/utils.py,sha256=SXR_r8a2Yp114_PohO_E20obUEs6pLd4vRoisvHsQP8,6057
fairscale-0.4.13.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
fairscale-0.4.13.dist-info/METADATA,sha256=vgqSh7w_Lb98mAG13qY71RdZmNrF5UgMptVSddzNW38,1098
fairscale-0.4.13.dist-info/RECORD,,
fairscale-0.4.13.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
fairscale-0.4.13.dist-info/WHEEL,sha256=_zCd3N1l69ArxyTb8rzEoP9TpbYXkqRFSNOD5OuxnTs,91
fairscale-0.4.13.dist-info/entry_points.txt,sha256=U3K2WDcAE0SUfMXnQExY3LdAXC81seEVDZVRKw9XZYc,67
fairscale-0.4.13.dist-info/licenses/LICENSE,sha256=Cpt3P1Ie3ikQIEVnu-acTyqO6L7eB93g5me8xt_SaKA,1739
fairscale-0.4.13.dist-info/licenses/NOTICE,sha256=yVg1OYKOQmvqafZRvnwgI_K-ojuAXpuoCI4d9o7K198,27290
fairscale-0.4.13.dist-info/top_level.txt,sha256=kFH3hRCYis7CzkdZYHjhHCUTQgoI1oPNLhMot5TFDWs,21
fairscale/__init__.py,sha256=7orq33mAnu0bS6lYj8NqO9CHzeP7h5_63eF1l847BHA,760
fairscale/__pycache__/__init__.cpython-313.pyc,,
fairscale/__pycache__/version.cpython-313.pyc,,
fairscale/clib/fused_adam_cuda/compat.h,sha256=8U6RUFjyVVP3Dhq1yxyyvuUfjhqphPWGOZyWW_xzGDQ,83
fairscale/clib/fused_adam_cuda/multi_tensor_apply.cuh,sha256=Wo6WUA88OalLxJHGftu-7DvOsd905ygoQaLLaXH0G1U,4455
fairscale/experimental/__init__.py,sha256=WjqqiNZQ3nXotSdYVfV8wbrGmROnXDVqGeUsvHbaDbQ,682
fairscale/experimental/__pycache__/__init__.cpython-313.pyc,,
fairscale/experimental/nn/__init__.py,sha256=xumLqSWdX-N5S7UcaDCDU7jbNAPMq3nL3dI-_-KYmYI,398
fairscale/experimental/nn/__pycache__/__init__.cpython-313.pyc,,
fairscale/experimental/nn/__pycache__/auto_shard.cpython-313.pyc,,
fairscale/experimental/nn/__pycache__/mevo.cpython-313.pyc,,
fairscale/experimental/nn/__pycache__/offload.cpython-313.pyc,,
fairscale/experimental/nn/__pycache__/sync_batchnorm.cpython-313.pyc,,
fairscale/experimental/nn/ampnet_pipe/__init__.py,sha256=wjvUlrCFrSDqoLedGWLw8FfKd2G4gBE8bRtva-By_pM,248
fairscale/experimental/nn/ampnet_pipe/__pycache__/__init__.cpython-313.pyc,,
fairscale/experimental/nn/ampnet_pipe/__pycache__/ampnet.cpython-313.pyc,,
fairscale/experimental/nn/ampnet_pipe/__pycache__/pipe.cpython-313.pyc,,
fairscale/experimental/nn/ampnet_pipe/ampnet.py,sha256=n_Gi_dHtQQ7usxw6OL5Uc_y9zdmEHDXrBivKKNALK0w,15011
fairscale/experimental/nn/ampnet_pipe/pipe.py,sha256=eA2Gry869i1AEZ7vBehsvPmBnItCtLbVAGHxC2W_VXo,2204
fairscale/experimental/nn/auto_shard.py,sha256=mZODv3HM3Xu8o1fYb_Nuu4PqfjpOGG4YA3bSS0zNRZQ,10164
fairscale/experimental/nn/data_parallel/__init__.py,sha256=_JOu5GSXX9f-LwG-E8VXz7KHoRMQKutLRUdJgZR8qaw,278
fairscale/experimental/nn/data_parallel/__pycache__/__init__.cpython-313.pyc,,
fairscale/experimental/nn/data_parallel/gossip/__init__.py,sha256=fzLj3SEZUFDFVn4UN-Br9eeyN7YdvG_y_KD1CWXh93c,700
fairscale/experimental/nn/data_parallel/gossip/__pycache__/__init__.cpython-313.pyc,,
fairscale/experimental/nn/data_parallel/gossip/__pycache__/distributed.cpython-313.pyc,,
fairscale/experimental/nn/data_parallel/gossip/__pycache__/gossiper.cpython-313.pyc,,
fairscale/experimental/nn/data_parallel/gossip/__pycache__/graph_manager.cpython-313.pyc,,
fairscale/experimental/nn/data_parallel/gossip/__pycache__/mixing_manager.cpython-313.pyc,,
fairscale/experimental/nn/data_parallel/gossip/distributed.py,sha256=09UQ2DQlY7xGwCC21-wGVe3u5NRAdULOFzZy2257Emw,54825
fairscale/experimental/nn/data_parallel/gossip/gossiper.py,sha256=-Y89aGCZLsiRGtTVB-didG_mdR8l_6UyTfoYZa3ryPk,10725
fairscale/experimental/nn/data_parallel/gossip/graph_manager.py,sha256=rEy6tbDIi2VCqMVqdiHNrtf8NiK8gZzTNHYAHn3VDLg,10652
fairscale/experimental/nn/data_parallel/gossip/mixing_manager.py,sha256=yufKER5dKeS78nTQF3bqFa0meaWF-yi7AtYcbDrCoNg,2011
fairscale/experimental/nn/data_parallel/gossip/utils/__init__.py,sha256=pUBKvNkzTmIpAnf8Qgpk6TLkF-DtqauTPOKW4KMixaI,373
fairscale/experimental/nn/data_parallel/gossip/utils/__pycache__/__init__.cpython-313.pyc,,
fairscale/experimental/nn/data_parallel/gossip/utils/__pycache__/cuda_metering.cpython-313.pyc,,
fairscale/experimental/nn/data_parallel/gossip/utils/__pycache__/helpers.cpython-313.pyc,,
fairscale/experimental/nn/data_parallel/gossip/utils/cuda_metering.py,sha256=eXuREsUM0cSo_vGUbmFstdwRVg3OcltqlF65Br0RSew,4149
fairscale/experimental/nn/data_parallel/gossip/utils/helpers.py,sha256=9tFIUoaqc-WeN-a3nAeBRfyo599O_GAde8ukC6sq4wI,5571
fairscale/experimental/nn/distributed_pipeline/__init__.py,sha256=gAxFWgMg6MxuaOAWocUvMoSGCk68gKi3UnEn_OwWVpc,294
fairscale/experimental/nn/distributed_pipeline/__pycache__/__init__.cpython-313.pyc,,
fairscale/experimental/nn/distributed_pipeline/__pycache__/data.cpython-313.pyc,,
fairscale/experimental/nn/distributed_pipeline/__pycache__/graph.cpython-313.pyc,,
fairscale/experimental/nn/distributed_pipeline/__pycache__/loss.cpython-313.pyc,,
fairscale/experimental/nn/distributed_pipeline/__pycache__/partition_handler.cpython-313.pyc,,
fairscale/experimental/nn/distributed_pipeline/__pycache__/pipeline.cpython-313.pyc,,
fairscale/experimental/nn/distributed_pipeline/__pycache__/trace.cpython-313.pyc,,
fairscale/experimental/nn/distributed_pipeline/data.py,sha256=orPpLKAqyQKFiAiUJo4AkiGIJFhGgF69RbE3UzxbC-I,598
fairscale/experimental/nn/distributed_pipeline/graph.py,sha256=2QQf2tek_20sTOKMRKX9J06QwxIViBrMoNqZEQ0oTpg,9480
fairscale/experimental/nn/distributed_pipeline/loss.py,sha256=9U7JqhyRjC5E8BWsEjvfb5oKyw96JKvsuj9djUUiu9k,762
fairscale/experimental/nn/distributed_pipeline/partition_handler.py,sha256=kXMub5-G5vSjC7LFKlJRNEpazKQOKnzD4ISPXHCStBQ,12228
fairscale/experimental/nn/distributed_pipeline/pipeline.py,sha256=M5dUfKjfuU9dEBln-tdgkmw8uqxBjK0Kii4ayQEoY2w,7991
fairscale/experimental/nn/distributed_pipeline/trace.py,sha256=xfogQA02wSdAqsvkEz9qS6_z40wTF2hg4eE7fdgCM-Q,4597
fairscale/experimental/nn/mevo.py,sha256=0gK0Y3jGcD_VxG1RyiJMNycnkTLYOhi9EGmjWWCFMh0,25261
fairscale/experimental/nn/offload.py,sha256=DiKR031hUcsMFiIoQJIvruzJxIi6Ln7GRAIYZZbeAsE,23348
fairscale/experimental/nn/sync_batchnorm.py,sha256=pYXsiOY_uKLukr0dDE_Ik88MgHx_Nd6Z7SUh3XDiyzc,9609
fairscale/experimental/optim/__init__.py,sha256=xFWaiIYoZZ67PYm8aQ81p9yEJ37HDzvReHWpmzh5LmM,279
fairscale/experimental/optim/__pycache__/__init__.cpython-313.pyc,,
fairscale/experimental/optim/__pycache__/dynamic_loss_scaler.cpython-313.pyc,,
fairscale/experimental/optim/dynamic_loss_scaler.py,sha256=ZdbxO-zIhV6j8HgF908x6CwrsWizFc_dN9TA0sFbNIo,7492
fairscale/experimental/tooling/__init__.py,sha256=wjvUlrCFrSDqoLedGWLw8FfKd2G4gBE8bRtva-By_pM,248
fairscale/experimental/tooling/__pycache__/__init__.cpython-313.pyc,,
fairscale/experimental/tooling/__pycache__/layer_memory_tracker.cpython-313.pyc,,
fairscale/experimental/tooling/layer_memory_tracker.py,sha256=hdyee6eU-HRDppcpci4_gH3zza755WjvOnY9PCvgmCY,29289
fairscale/experimental/wgit/__init__.py,sha256=-Zg0KssxCSrZeT549tgYUGoZshjrpK8iy2eBlFfPiss,803
fairscale/experimental/wgit/__main__.py,sha256=CIuDrfvcNHs4xvu6TexSYM9zLFwjeOIysznqMWVVssI,239
fairscale/experimental/wgit/__pycache__/__init__.cpython-313.pyc,,
fairscale/experimental/wgit/__pycache__/__main__.cpython-313.pyc,,
fairscale/experimental/wgit/__pycache__/cli.cpython-313.pyc,,
fairscale/experimental/wgit/__pycache__/pygit.cpython-313.pyc,,
fairscale/experimental/wgit/__pycache__/repo.cpython-313.pyc,,
fairscale/experimental/wgit/__pycache__/sha1_store.cpython-313.pyc,,
fairscale/experimental/wgit/__pycache__/signal_sparsity.cpython-313.pyc,,
fairscale/experimental/wgit/__pycache__/signal_sparsity_profiling.cpython-313.pyc,,
fairscale/experimental/wgit/__pycache__/utils.cpython-313.pyc,,
fairscale/experimental/wgit/__pycache__/version.cpython-313.pyc,,
fairscale/experimental/wgit/cli.py,sha256=-CfaO8eGLU1grbdGY8A_5xh-5hfIlXlMGQytzoSYEnk,3360
fairscale/experimental/wgit/pygit.py,sha256=kD_il5v6RqCm_DslK8DPsNCIt9J22DI-yoW5VOm4Fvo,6121
fairscale/experimental/wgit/repo.py,sha256=VNsCbKSbbdrqRUyjbrb9c8kAqF7ka3xqenttAlfklYk,19158
fairscale/experimental/wgit/sha1_store.py,sha256=K9E-t2zyP--D1OK86ofJ89Jt5CvqvD7d3chvEcKS514,20700
fairscale/experimental/wgit/signal_sparsity.py,sha256=GEO948Rei1dcLUHXTQyGBq84sutuhGkTdt9EieRjwro,18111
fairscale/experimental/wgit/signal_sparsity_profiling.py,sha256=GXrx5DTJ2CkASdw5d7JkUAMiwMl4KeZ3vnXR-KYzjVE,2370
fairscale/experimental/wgit/utils.py,sha256=iqPFMENmDwfrjRAfLIzEgMfLJXQoDI6uIwrG6oOrpj8,356
fairscale/experimental/wgit/version.py,sha256=SFJXggBmZ4nvwi462SKDq8YH5Vu7a0JvMRWER7QvCM4,30
fairscale/fair_dev/__init__.py,sha256=wjvUlrCFrSDqoLedGWLw8FfKd2G4gBE8bRtva-By_pM,248
fairscale/fair_dev/__pycache__/__init__.cpython-313.pyc,,
fairscale/fair_dev/__pycache__/common_paths.cpython-313.pyc,,
fairscale/fair_dev/common_paths.py,sha256=ujhJKLZnNAA6qYqnjkmopqRsmn_MUjp5_isy_Kxic3I,295
fairscale/fair_dev/testing/__init__.py,sha256=wjvUlrCFrSDqoLedGWLw8FfKd2G4gBE8bRtva-By_pM,248
fairscale/fair_dev/testing/__pycache__/__init__.cpython-313.pyc,,
fairscale/fair_dev/testing/__pycache__/golden_testing_data.cpython-313.pyc,,
fairscale/fair_dev/testing/__pycache__/testing.cpython-313.pyc,,
fairscale/fair_dev/testing/__pycache__/testing_memory.cpython-313.pyc,,
fairscale/fair_dev/testing/golden_testing_data.py,sha256=kU6EJ-GQg7vNj--5rRqZrxgK4wIVFYOmc7QoL2vIVSk,2204
fairscale/fair_dev/testing/testing.py,sha256=o7GoBbnsTnUOW5419m8e9lddeHiKxHDXp3xHY7Utc8A,27571
fairscale/fair_dev/testing/testing_memory.py,sha256=rFmCv3LONAl7L1jy4zDdf190QxI6OmJEDrp7Wjd808U,1119
fairscale/internal/__init__.py,sha256=sTUAbHlQSb9n5agf5qK6x5UZK__dt0feIyV5kEL8_Dg,222
fairscale/internal/__pycache__/__init__.cpython-313.pyc,,
fairscale/internal/__pycache__/containers.cpython-313.pyc,,
fairscale/internal/__pycache__/object.cpython-313.pyc,,
fairscale/internal/__pycache__/parallel.cpython-313.pyc,,
fairscale/internal/__pycache__/params.cpython-313.pyc,,
fairscale/internal/__pycache__/reduce_scatter_bucketer.cpython-313.pyc,,
fairscale/internal/__pycache__/state_dict.cpython-313.pyc,,
fairscale/internal/__pycache__/version.cpython-313.pyc,,
fairscale/internal/containers.py,sha256=0hOM9lwzeXa2n9XDIigilyObT_wxhJkB4iWHe_u2y0Q,5971
fairscale/internal/object.py,sha256=byPVSbvybc4j9I-7b7FtZpWnYqOOYCzLTmiTMEh066g,934
fairscale/internal/parallel.py,sha256=M614LWKYbViR0468aliJcwQKsZvNA_0ZcR2H--rn8Po,6089
fairscale/internal/params.py,sha256=GENPuCtNqP-U9iGodLBqnKm5rKy8DL1YKTXxcYH6MTk,2603
fairscale/internal/reduce_scatter_bucketer.py,sha256=GWXGQQYn-c1a2oMs-kaanTX2NseBgCYzafb_dhmG7rw,8398
fairscale/internal/state_dict.py,sha256=PtlFPfgixaILrKL2rYUYF_L40SzivyBRFk79tmZGO-M,2615
fairscale/internal/version.py,sha256=aO0tddb335JRe4fPt1FoNvVJD7SBsoQM3KCWvGgPxdY,1091
fairscale/nn/__init__.py,sha256=YUX2G_YP_iJPTh64DXz8trgQvj1VSwoUPkkt83eIOe8,736
fairscale/nn/__pycache__/__init__.cpython-313.pyc,,
fairscale/nn/checkpoint/__init__.py,sha256=qHny1bRRs-5O35sJx7jQ4MXLZH1csPwpOwA5v6k2-pI,338
fairscale/nn/checkpoint/__pycache__/__init__.cpython-313.pyc,,
fairscale/nn/checkpoint/__pycache__/checkpoint_activations.cpython-313.pyc,,
fairscale/nn/checkpoint/__pycache__/checkpoint_utils.cpython-313.pyc,,
fairscale/nn/checkpoint/checkpoint_activations.py,sha256=ebtpvvZPF8Z00lNmKEcgWfEblkZ0GzuTm3IppaCv3Dc,13649
fairscale/nn/checkpoint/checkpoint_utils.py,sha256=o7hQMwblwVL1ymh1qeV6edPSXWfd2Cp8sQWPpL4IFi4,1894
fairscale/nn/data_parallel/__init__.py,sha256=ZSRmeXWIMp0lFt87J9zvXQldZqMu2OeZjcnJZJuT-So,587
fairscale/nn/data_parallel/__pycache__/__init__.cpython-313.pyc,,
fairscale/nn/data_parallel/__pycache__/fsdp_optim_utils.cpython-313.pyc,,
fairscale/nn/data_parallel/__pycache__/fully_sharded_data_parallel.cpython-313.pyc,,
fairscale/nn/data_parallel/__pycache__/sharded_ddp.cpython-313.pyc,,
fairscale/nn/data_parallel/fsdp_optim_utils.py,sha256=6T5cGWrTqi_0BoF_yHOOsGvmF5LuhPxbUmsh9XLOhU4,11903
fairscale/nn/data_parallel/fully_sharded_data_parallel.py,sha256=CxkL0Ij7_8m2-Nmh8ye0zFg5ajVXHjqDD-FORuMzK_o,136275
fairscale/nn/data_parallel/sharded_ddp.py,sha256=kGAU-irdXGSMtnIW_OLzR3cmGxJvRPWXA0UWaKZW7DY,30588
fairscale/nn/misc/__init__.py,sha256=RYQE_w4X-HWzr0UsIvyxq2zCnsjFEi_PzmUZlZg4XyI,597
fairscale/nn/misc/__pycache__/__init__.cpython-313.pyc,,
fairscale/nn/misc/__pycache__/flatten_params_wrapper.cpython-313.pyc,,
fairscale/nn/misc/__pycache__/param_bucket.cpython-313.pyc,,
fairscale/nn/misc/flatten_params_wrapper.py,sha256=pmvGIyExGqD6DvCzBiU9d9pgfRoaX6raVt3n5XeRvSA,23006
fairscale/nn/misc/param_bucket.py,sha256=1qzph6a4C8BtPT0g4n42YWsvUGM5UN_MZU5HHe8K0ag,8449
fairscale/nn/model_parallel/__init__.py,sha256=hDK9s3uc7vwyvjoC7GDGuevrYcVfsc4y7eO4eTInqf4,921
fairscale/nn/model_parallel/__pycache__/__init__.cpython-313.pyc,,
fairscale/nn/model_parallel/__pycache__/cross_entropy.cpython-313.pyc,,
fairscale/nn/model_parallel/__pycache__/initialize.cpython-313.pyc,,
fairscale/nn/model_parallel/__pycache__/layers.cpython-313.pyc,,
fairscale/nn/model_parallel/__pycache__/mappings.cpython-313.pyc,,
fairscale/nn/model_parallel/__pycache__/random.cpython-313.pyc,,
fairscale/nn/model_parallel/__pycache__/utils.cpython-313.pyc,,
fairscale/nn/model_parallel/cross_entropy.py,sha256=lnUgIdyliJAzlZA2tkJgM2qXjpjJ65xwmFv7nqrMDJY,4711
fairscale/nn/model_parallel/initialize.py,sha256=ghmc8VZV005fTrzx8B4lwUXRSBVeMey3aUPxaB8neK0,7384
fairscale/nn/model_parallel/layers.py,sha256=zxXMEYpgql-LdgEmxTz2Y5u83kidV16ZA-eumRX4iFs,14361
fairscale/nn/model_parallel/mappings.py,sha256=khHUJlgFMKITAWJAfI9B2m56ER_BWQFXyuVpeDf52vI,4767
fairscale/nn/model_parallel/random.py,sha256=bIT5Ri1qwWUE3ARq-TKLwespwhOBBCQv5z6R0szH34M,9699
fairscale/nn/model_parallel/utils.py,sha256=zdZzvgUg4GqMxNk3ZgBJakNbxErlAzMTtwEluSR8_3w,3072
fairscale/nn/moe/__init__.py,sha256=jnCr3GIqljcaVarZ2p0OUGAJ5x9wG8rYX5WqKSbc-Ro,312
fairscale/nn/moe/__pycache__/__init__.cpython-313.pyc,,
fairscale/nn/moe/__pycache__/moe_layer.cpython-313.pyc,,
fairscale/nn/moe/__pycache__/top2gate.cpython-313.pyc,,
fairscale/nn/moe/moe_layer.py,sha256=IFCVR11h7DMfiLTODpv_Ije6ff6Q8NmQhVcLmqkMsRE,3762
fairscale/nn/moe/top2gate.py,sha256=mQTNMHwgNzTxEf0O4HZNDmYWkTURu948lJpBHpQdNlI,4952
fairscale/nn/pipe/__init__.py,sha256=Zld2P0Me9H5qVrD0Tz5KKXyHmjV9HGBuWSD2RunqySk,1061
fairscale/nn/pipe/__pycache__/__init__.cpython-313.pyc,,
fairscale/nn/pipe/__pycache__/async_pipe.cpython-313.pyc,,
fairscale/nn/pipe/__pycache__/async_pipeline.cpython-313.pyc,,
fairscale/nn/pipe/__pycache__/async_schedule.cpython-313.pyc,,
fairscale/nn/pipe/__pycache__/batchnorm.cpython-313.pyc,,
fairscale/nn/pipe/__pycache__/checkpoint.cpython-313.pyc,,
fairscale/nn/pipe/__pycache__/copy.cpython-313.pyc,,
fairscale/nn/pipe/__pycache__/dependency.cpython-313.pyc,,
fairscale/nn/pipe/__pycache__/messages.cpython-313.pyc,,
fairscale/nn/pipe/__pycache__/microbatch.cpython-313.pyc,,
fairscale/nn/pipe/__pycache__/phony.cpython-313.pyc,,
fairscale/nn/pipe/__pycache__/pipe.cpython-313.pyc,,
fairscale/nn/pipe/__pycache__/pipeline.cpython-313.pyc,,
fairscale/nn/pipe/__pycache__/rpc.cpython-313.pyc,,
fairscale/nn/pipe/__pycache__/stream.cpython-313.pyc,,
fairscale/nn/pipe/__pycache__/types.cpython-313.pyc,,
fairscale/nn/pipe/__pycache__/worker.cpython-313.pyc,,
fairscale/nn/pipe/async_pipe.py,sha256=EVCANSwv10MP4ajsANuyQay9tpnMMJbqkqUgfJLZcO4,14675
fairscale/nn/pipe/async_pipeline.py,sha256=GiW3WbOxET60WHMmhj7MlxpqtSHLyHdbaEiKCeDEl-k,3072
fairscale/nn/pipe/async_schedule.py,sha256=7jDc0VGgbe7d2I07mmOG-3sDw43YOk2vCojgbDcxF28,19047
fairscale/nn/pipe/balance/__init__.py,sha256=0o7SlmYQ5aQr0FnzRoGBwSkZP0Dnj0bKbLDwtrKfoTQ,6088
fairscale/nn/pipe/balance/__pycache__/__init__.cpython-313.pyc,,
fairscale/nn/pipe/balance/__pycache__/blockpartition.cpython-313.pyc,,
fairscale/nn/pipe/balance/__pycache__/profile.cpython-313.pyc,,
fairscale/nn/pipe/balance/blockpartition.py,sha256=ka4Gs7AnSw7uY4P_J5qjytEDljzzHc42VP2ptvyJutk,3705
fairscale/nn/pipe/balance/profile.py,sha256=Sel0yEPC7qo3RJN4rfBPy1vq3zaE_l83xpLrYG8k_6w,4136
fairscale/nn/pipe/batchnorm.py,sha256=8I0y2Zqs5Y3yGWu47czBvxI20U8BGXolPmJGZD6TRuo,6071
fairscale/nn/pipe/checkpoint.py,sha256=umH7i_tAFI3bEVkPuPM_DDEfGRoHMzvnksONr8oFslA,10307
fairscale/nn/pipe/copy.py,sha256=JMeXnPHq8K0UGflJ-otoojhdFpPlQ_I5iYbKujmCU1g,4214
fairscale/nn/pipe/dependency.py,sha256=dAsN-tJqnoqEqcT6JVhEcKdqBG5OEOcwI4fIqfKiHeY,2198
fairscale/nn/pipe/messages.py,sha256=-aKCFHpvfe621vyZ7pAeiD1U4zy5eWi1QB7IhvEpPlo,6155
fairscale/nn/pipe/microbatch.py,sha256=ETs5khNVxECfH-Qd82jEH9bP3si2JmkkFtg7Vv1Tvus,5949
fairscale/nn/pipe/phony.py,sha256=3B27N1JagHmvU1Sl9tFD2zFxxrj7-wwFcx9lMWnoVRc,2220
fairscale/nn/pipe/pipe.py,sha256=xFKrRjek-PIJrKTqV3KbsboxDg9hdzzDqt_7N3CYrlo,14149
fairscale/nn/pipe/pipeline.py,sha256=MO2ADAEAxVttbw_QC8Bxt7Oec_vMDj0hAgMW_EGDcUM,10097
fairscale/nn/pipe/rpc.py,sha256=PHyboj_AnFuN9aK-Mu80py9gL-cssxdP8hZ3ANOpfEs,10227
fairscale/nn/pipe/skip/__init__.py,sha256=cVOILZxpn_cALETVbtyz5UPTHZ6d1RSB6hi5GYzSzTM,994
fairscale/nn/pipe/skip/__pycache__/__init__.cpython-313.pyc,,
fairscale/nn/pipe/skip/__pycache__/layout.cpython-313.pyc,,
fairscale/nn/pipe/skip/__pycache__/namespace.cpython-313.pyc,,
fairscale/nn/pipe/skip/__pycache__/portal.cpython-313.pyc,,
fairscale/nn/pipe/skip/__pycache__/skippable.cpython-313.pyc,,
fairscale/nn/pipe/skip/__pycache__/tracker.cpython-313.pyc,,
fairscale/nn/pipe/skip/layout.py,sha256=K3b0uT8YKUjhnQ-oxpR-XbR1DTdUROn3OI12dIV-dTY,4704
fairscale/nn/pipe/skip/namespace.py,sha256=U0CAIsRbtAmiJEE0W4IBqk9fAb3-fP4B_g6Bk6XyJO8,2006
fairscale/nn/pipe/skip/portal.py,sha256=4nvRftjyoLoYCU4vRaERDhdjC9zDL4mZzaO4Pz7FOYI,8302
fairscale/nn/pipe/skip/skippable.py,sha256=D4n5N7J6AobGsZp06J2dfeGllVbDc8tiFtP_SrlhwDQ,14868
fairscale/nn/pipe/skip/tracker.py,sha256=cDJqh1PXhK_qzQY7DA7hpJhs4RrqXFeJ6ejkzxOoqdA,6851
fairscale/nn/pipe/stream.py,sha256=aF_GOC98mHDz8YQ0ZqOEGwoGcozkczbv5wqP0iKsmtY,4325
fairscale/nn/pipe/types.py,sha256=5w_GXm24K6hkakUfgcSA0MCN_FU03jd_AerK-f4edLs,1714
fairscale/nn/pipe/worker.py,sha256=hr6-rv54Vt2YBUSMJdtsICHkR83VLHSSUVQOnBexo7g,5409
fairscale/nn/wrap/__init__.py,sha256=wydV-mrMKUfvc6jWx_wKcUy2FnrIeCZui8kj4jEbbg4,352
fairscale/nn/wrap/__pycache__/__init__.cpython-313.pyc,,
fairscale/nn/wrap/__pycache__/auto_wrap.cpython-313.pyc,,
fairscale/nn/wrap/auto_wrap.py,sha256=IspBlM4fsDmy5rfuKNZt_dL32RO1A-IG2qyiFlGPRGs,14079
fairscale/optim/__init__.py,sha256=EAXRm9JWIOjgT4NTmkfQjUttIi-hXHcszq7kbiBErZY,674
fairscale/optim/__pycache__/__init__.cpython-313.pyc,,
fairscale/optim/__pycache__/adam.cpython-313.pyc,,
fairscale/optim/__pycache__/adascale.cpython-313.pyc,,
fairscale/optim/__pycache__/grad_scaler.cpython-313.pyc,,
fairscale/optim/__pycache__/layerwise_gradient_scaler.cpython-313.pyc,,
fairscale/optim/__pycache__/oss.cpython-313.pyc,,
fairscale/optim/adam.py,sha256=M9Lpe_yPnsBKUVm3RHVkkuTzRYWLiHDIL1PQ2p9rcBo,13276
fairscale/optim/adascale.py,sha256=9VnqphvQRhD5ufU9Uh-KbMFHUMzmcxK-OoE4Xl3wuuw,31382
fairscale/optim/grad_scaler.py,sha256=kHwcKQ4kAw6OASCwl4yTf-sJ02rhZ1FScyPvBFPvheQ,19622
fairscale/optim/layerwise_gradient_scaler.py,sha256=FEK35m57kI-lsGZ9cC9OiDSb20mL0WH3Ovjt5tKQFGU,12082
fairscale/optim/oss.py,sha256=Stqt7c26sGZj4-w8RdyMxvFCKRTPWDlSdqlkm5P31NM,30470
fairscale/version.py,sha256=65nslGv7biWPiGVg7L-DA9xw6IuFbxj2m-VFEMwTBY8,31
