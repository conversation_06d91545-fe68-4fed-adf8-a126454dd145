#!/usr/bin/env python3
"""
Test script to verify Chain-of-Zoom setup is working correctly.
This script tests the basic components without requiring gated models.
"""

import os
import sys
import torch
from PIL import Image
import numpy as np
from torchvision import transforms

def test_basic_imports():
    """Test that all required packages can be imported."""
    print("🔍 Testing basic imports...")
    
    try:
        import torch
        import torchvision
        import transformers
        import diffusers
        import accelerate
        import huggingface_hub
        import peft
        import timm
        import scipy
        import numpy as np
        from PIL import Image
        print("✅ All basic packages imported successfully!")
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_pytorch_setup():
    """Test PyTorch setup and device availability."""
    print("\n🔍 Testing PyTorch setup...")
    
    print(f"PyTorch version: {torch.__version__}")
    print(f"CUDA available: {torch.cuda.is_available()}")
    
    if torch.cuda.is_available():
        print(f"CUDA device count: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            print(f"  Device {i}: {torch.cuda.get_device_name(i)}")
    else:
        print("Running on CPU (CUDA not available)")
    
    # Test basic tensor operations
    try:
        x = torch.randn(3, 3)
        y = torch.randn(3, 3)
        z = torch.matmul(x, y)
        print("✅ Basic tensor operations working!")
        return True
    except Exception as e:
        print(f"❌ Tensor operation error: {e}")
        return False

def test_ram_model():
    """Test RAM model loading."""
    print("\n🔍 Testing RAM model...")
    
    try:
        # Check if RAM checkpoint exists
        ram_checkpoint = "ckpt/RAM/ram_swin_large_14m.pth"
        if os.path.exists(ram_checkpoint):
            print(f"✅ RAM checkpoint found: {ram_checkpoint}")
            file_size = os.path.getsize(ram_checkpoint) / (1024**3)  # GB
            print(f"   File size: {file_size:.2f} GB")
        else:
            print(f"❌ RAM checkpoint not found: {ram_checkpoint}")
            return False
            
        # Try importing RAM modules
        from ram.models.ram_lora import ram
        from ram import inference_ram as inference
        print("✅ RAM modules imported successfully!")
        return True
        
    except Exception as e:
        print(f"❌ RAM model error: {e}")
        return False

def test_sample_images():
    """Test sample images loading."""
    print("\n🔍 Testing sample images...")
    
    samples_dir = "samples"
    if not os.path.exists(samples_dir):
        print(f"❌ Samples directory not found: {samples_dir}")
        return False
    
    sample_files = [f for f in os.listdir(samples_dir) if f.endswith('.png')]
    print(f"Found {len(sample_files)} sample images:")
    
    for sample_file in sample_files:
        sample_path = os.path.join(samples_dir, sample_file)
        try:
            img = Image.open(sample_path)
            print(f"  ✅ {sample_file}: {img.size} pixels, mode: {img.mode}")
        except Exception as e:
            print(f"  ❌ {sample_file}: Error loading - {e}")
            return False
    
    return len(sample_files) > 0

def test_image_processing():
    """Test basic image processing pipeline."""
    print("\n🔍 Testing image processing...")
    
    try:
        # Load a sample image
        sample_path = "samples/0064.png"
        if not os.path.exists(sample_path):
            print(f"❌ Sample image not found: {sample_path}")
            return False
        
        # Load and process image
        img = Image.open(sample_path).convert('RGB')
        print(f"Original image size: {img.size}")
        
        # Test transforms
        tensor_transforms = transforms.Compose([
            transforms.ToTensor(),
        ])
        
        img_tensor = tensor_transforms(img)
        print(f"Tensor shape: {img_tensor.shape}")
        print(f"Tensor dtype: {img_tensor.dtype}")
        print(f"Tensor range: [{img_tensor.min():.3f}, {img_tensor.max():.3f}]")
        
        # Test resize and crop
        def resize_and_center_crop(img: Image.Image, size: int) -> Image.Image:
            w, h = img.size
            scale = size / min(w, h)
            new_w, new_h = int(w * scale), int(h * scale)
            img = img.resize((new_w, new_h), Image.LANCZOS)
            left = (new_w - size) // 2
            top  = (new_h - size) // 2
            return img.crop((left, top, left + size, top + size))
        
        processed_img = resize_and_center_crop(img, 512)
        print(f"Processed image size: {processed_img.size}")
        
        print("✅ Image processing pipeline working!")
        return True
        
    except Exception as e:
        print(f"❌ Image processing error: {e}")
        return False

def test_checkpoints():
    """Test availability of model checkpoints."""
    print("\n🔍 Testing model checkpoints...")
    
    checkpoint_dirs = {
        "SR_LoRA": "ckpt/SR_LoRA",
        "SR_VAE": "ckpt/SR_VAE", 
        "DAPE": "ckpt/DAPE",
        "RAM": "ckpt/RAM"
    }
    
    all_good = True
    for name, path in checkpoint_dirs.items():
        if os.path.exists(path):
            files = os.listdir(path)
            if files:
                print(f"  ✅ {name}: {len(files)} files found")
            else:
                print(f"  ⚠️  {name}: Directory exists but empty")
        else:
            print(f"  ❌ {name}: Directory not found")
            all_good = False
    
    return all_good

def main():
    """Run all tests."""
    print("🚀 Chain-of-Zoom Setup Test")
    print("=" * 50)
    
    tests = [
        ("Basic Imports", test_basic_imports),
        ("PyTorch Setup", test_pytorch_setup),
        ("RAM Model", test_ram_model),
        ("Sample Images", test_sample_images),
        ("Image Processing", test_image_processing),
        ("Model Checkpoints", test_checkpoints),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("📊 Test Summary:")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n🎉 All tests passed! Chain-of-Zoom setup is ready!")
        print("\nNext steps:")
        print("1. Get access to Stable Diffusion 3 model on Hugging Face")
        print("2. Run: huggingface-cli login")
        print("3. Try the full inference pipeline")
    else:
        print(f"\n⚠️  {len(results) - passed} tests failed. Please check the setup.")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
