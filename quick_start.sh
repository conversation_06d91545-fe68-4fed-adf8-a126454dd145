#!/bin/bash

# Chain-of-Zoom Quick Start Script
# Usage: ./quick_start.sh /path/to/your/image.jpg

echo "🚀 Chain-of-Zoom Quick Start"
echo "=============================="

# Check if image path provided
if [ $# -eq 0 ]; then
    echo "❌ Please provide an image path"
    echo "Usage: ./quick_start.sh /path/to/your/image.jpg"
    echo ""
    echo "Examples:"
    echo "  ./quick_start.sh ~/Desktop/my_photo.jpg"
    echo "  ./quick_start.sh ~/Downloads/image.png"
    exit 1
fi

IMAGE_PATH="$1"

# Check if image exists
if [ ! -f "$IMAGE_PATH" ]; then
    echo "❌ Image not found: $IMAGE_PATH"
    exit 1
fi

echo "📸 Processing image: $IMAGE_PATH"

# Activate virtual environment
source coz_env/bin/activate

# Create output directory with timestamp
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
OUTPUT_DIR="results_${TIMESTAMP}"

echo "📁 Output directory: $OUTPUT_DIR"

# Run Chain-of-Zoom demo
echo "🔄 Running Chain-of-Zoom with AI captioning..."
python demo_basic_upscaling.py \
    -i "$IMAGE_PATH" \
    --output_dir "$OUTPUT_DIR" \
    --test_ram \
    --scale_factor 4 \
    --num_steps 3

# Check if successful
if [ $? -eq 0 ]; then
    echo ""
    echo "✅ Processing complete!"
    echo "📁 Results saved in: $OUTPUT_DIR"
    echo ""
    echo "🖼️  Generated files:"
    ls -la "$OUTPUT_DIR"
    echo ""
    echo "💡 To view results:"
    echo "   open $OUTPUT_DIR"
else
    echo "❌ Processing failed"
    exit 1
fi
